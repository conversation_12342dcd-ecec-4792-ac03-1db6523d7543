from manim import *
import numpy as np

class PentagonConstruction(Scene):
    def construct(self):
        # 参数设置：圆半径
        R = 3

        # 1. 画出以 O 为中心、半径为 R 的圆
        circle = Circle(radius=R, color=WHITE)
        circle.shift(ORIGIN)
        self.play(Create(circle))
        O = circle.get_center()
        center_dot = Dot(O, color=YELLOW)
        self.play(FadeIn(center_dot))
        self.wait(0.5)

        # 2. 在圆上取一点 A（右侧的交点）作为第一个顶点
        A = circle.point_at_angle(0)
        dot_A = Dot(A, color=RED)
        label_A = MathTex("A").next_to(dot_A, RIGHT)
        self.play(FadeIn(dot_A), Write(label_A))
        self.wait(0.5)

        # 3. 根据正五边形性质，边长 s = 2R*sin36°；
        #    对于 R = 3，有 s = 6*sin36° ≈ 3.527
        s = 2 * R * np.sin(np.radians(36))
        s_formula = MathTex("s=2R\\sin36^\\circ").to_corner(UL)
        self.play(Write(s_formula))
        self.wait(0.5)

        # 4. 根据圆中弦长公式：弦长 = 2R*sin(θ/2)，
        #    由 s = 2R*sin(θ/2) 可得 θ = 2*arcsin(s/(2R))
        theta = 2 * np.arcsin(s / (2 * R))
        # 理论上 theta 应为 72°（即 2π/5）
        # 在下面的步骤中，我们将依次以 s 为半径“踏弦”于圆上得到后续顶点

        # 5. 从 A 出发，用圆规（即固定长度 s）在圆上“走”出其余顶点
        vertices = [A]  # 存储各顶点坐标
        vertex_dots = [dot_A]
        vertex_labels = [label_A]
        for i in range(1, 5):
            # 计算旋转角度（依次累加 72°）
            current_angle = i * theta
            vertex = circle.point_at_angle(current_angle)
            vertices.append(vertex)
            dot = Dot(vertex, color=RED)
            # 为了避免标签重叠，可根据顶点所在位置调整标签位置（此处简单放置于右侧）
            label = MathTex(chr(65 + i)).next_to(dot, RIGHT)
            vertex_dots.append(dot)
            vertex_labels.append(label)

            # 模拟“用圆规踏弦”：从上一个顶点画一段弧，其半径固定为 s，
            # 这段弧正好将上一个顶点与当前顶点连接起来
            arc = ArcBetweenPoints(start=vertices[i - 1], end=vertex, angle=theta / 2, color=BLUE)
            self.play(Create(arc), run_time=0.5)
            self.play(FadeIn(dot), Write(label))
            self.wait(0.3)

        # 6. 将五个顶点依次连线构成正五边形
        pentagon = Polygon(*vertices, color=GREEN, stroke_width=2)
        self.play(Create(pentagon))
        self.wait(2)
