@echo off
chcp 65001 >nul
title PDF Chaser - 论文PDF批量下载工具

echo.
echo ============================================================
echo PDF Chaser - 论文PDF批量下载工具
echo ============================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要的Python包
echo 检查依赖包...
python -c "import pandas, requests" >nul 2>&1
if errorlevel 1 (
    echo 正在安装必要的依赖包...
    pip install pandas requests openpyxl
    if errorlevel 1 (
        echo 依赖包安装失败，请手动运行：pip install pandas requests openpyxl
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo.

REM 运行PDF Chaser
python run_pdf_chaser.py

echo.
echo 程序运行完成，按任意键退出...
pause >nul
