#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Chaser 使用示例
演示如何使用PDF Chaser的各种功能
"""

from pdf_chaser import PDFChaser
import json

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建PDF下载器实例
    chaser = PDFChaser()
    
    # 1. 合并xls文件
    print("1. 合并xls文件...")
    df = chaser.merge_xls_files()
    print(f"   合并完成，总共 {len(df)} 条记录")
    
    # 2. 提取DOI
    print("2. 提取DOI...")
    dois = chaser.extract_dois(df)
    print(f"   提取到 {len(dois)} 个有效DOI")
    
    # 3. 显示前几个DOI
    print("3. 前5个DOI示例:")
    for i, doi in enumerate(dois[:5], 1):
        print(f"   {i}. {doi}")
    
    return chaser, dois

def example_download_single():
    """单个PDF下载示例"""
    print("\n=== 单个PDF下载示例 ===")
    
    chaser = PDFChaser()
    
    # 使用一个已知的DOI进行测试
    test_doi = "10.1186/s41239-019-0171-0"
    print(f"测试下载DOI: {test_doi}")
    
    result = chaser.download_pdf_from_scihub(test_doi)
    if result:
        print(f"✓ 下载成功: {result}")
    else:
        print("✗ 下载失败")

def example_batch_download():
    """批量下载示例（少量文件）"""
    print("\n=== 批量下载示例 ===")
    
    chaser = PDFChaser()
    
    # 获取DOI列表
    df = chaser.merge_xls_files()
    all_dois = chaser.extract_dois(df)
    
    # 只下载前5个作为示例
    test_dois = all_dois[:5]
    print(f"示例：下载前5个DOI")
    
    # 开始批量下载
    stats = chaser.download_all_pdfs(test_dois)
    
    print(f"下载统计:")
    print(f"  总数: {stats['total']}")
    print(f"  成功: {stats['successful']}")
    print(f"  失败: {stats['failed']}")
    print(f"  成功率: {stats['successful']/stats['total']*100:.1f}%")

def example_progress_management():
    """进度管理示例"""
    print("\n=== 进度管理示例 ===")
    
    chaser = PDFChaser()
    
    # 加载当前进度
    downloaded = chaser.load_progress()
    print(f"当前已下载: {len(downloaded)} 个PDF")
    
    if downloaded:
        print("已下载的DOI:")
        for doi in list(downloaded)[:3]:
            print(f"  - {doi}")
        if len(downloaded) > 3:
            print(f"  ... 还有 {len(downloaded) - 3} 个")
    
    # 检查进度文件
    if chaser.progress_file.exists():
        with open(chaser.progress_file, 'r', encoding='utf-8') as f:
            progress = json.load(f)
            print(f"进度文件最后更新: {progress.get('last_update', '未知')}")

def example_configuration():
    """配置示例"""
    print("\n=== 配置示例 ===")
    
    # 创建自定义配置的下载器
    chaser = PDFChaser()
    
    print("当前配置:")
    print(f"  最小延迟: {chaser.min_delay} 秒")
    print(f"  最大延迟: {chaser.max_delay} 秒")
    print(f"  最大重试次数: {chaser.max_retries}")
    print(f"  请求超时: {chaser.timeout} 秒")
    print(f"  Sci-Hub镜像数量: {len(chaser.scihub_urls)}")
    print(f"  User-Agent数量: {len(chaser.user_agents)}")
    
    # 可以动态修改配置
    print("\n修改配置示例:")
    chaser.min_delay = 1  # 减少最小延迟
    chaser.max_delay = 3  # 减少最大延迟
    print(f"  新的延迟范围: {chaser.min_delay}-{chaser.max_delay} 秒")

def example_file_management():
    """文件管理示例"""
    print("\n=== 文件管理示例 ===")
    
    chaser = PDFChaser()
    
    # 检查目录结构
    print("目录结构:")
    print(f"  数据目录: {chaser.data_dir}")
    print(f"  xls文件目录: {chaser.doc_info_dir}")
    print(f"  PDF文件目录: {chaser.doc_pdf_dir}")
    print(f"  进度文件: {chaser.progress_file}")
    
    # 检查文件数量
    if chaser.doc_info_dir.exists():
        xls_files = list(chaser.doc_info_dir.glob("*.xls"))
        print(f"  xls文件数量: {len(xls_files)}")
    
    if chaser.doc_pdf_dir.exists():
        pdf_files = list(chaser.doc_pdf_dir.glob("*.pdf"))
        print(f"  PDF文件数量: {len(pdf_files)}")
        
        if pdf_files:
            total_size = sum(f.stat().st_size for f in pdf_files)
            print(f"  PDF文件总大小: {total_size / 1024 / 1024:.1f} MB")

def main():
    """主函数 - 运行所有示例"""
    print("PDF Chaser 使用示例")
    print("=" * 50)
    
    try:
        # 基本使用
        chaser, dois = example_basic_usage()
        
        # 进度管理
        example_progress_management()
        
        # 配置示例
        example_configuration()
        
        # 文件管理
        example_file_management()
        
        # 单个下载示例（可选）
        print("\n是否要测试单个PDF下载？(y/n): ", end="")
        if input().lower().startswith('y'):
            example_download_single()
        
        # 批量下载示例（可选）
        print("\n是否要测试批量下载（前5个DOI）？(y/n): ", end="")
        if input().lower().startswith('y'):
            example_batch_download()
        
        print("\n示例运行完成！")
        
    except Exception as e:
        print(f"示例运行出错: {e}")

if __name__ == "__main__":
    main()
