# PDF Chaser - 论文PDF批量下载工具

## 功能介绍

PDF Chaser 是一个用于从 Sci-Hub 批量下载论文PDF的工具，具有以下特性：

- 🔄 **自动合并xls文件**：合并 `data/doc_info` 目录下的所有Web of Science导出的xls文件
- 📄 **智能DOI提取**：自动提取并验证DOI格式，去除重复项
- 🌐 **多镜像支持**：支持多个Sci-Hub镜像站点，自动切换
- 🛡️ **防反爬虫**：随机延迟、多User-Agent、错误重试机制
- 💾 **进度保存**：支持中断恢复，下次运行时从上次停止处继续
- 📊 **详细日志**：完整的下载日志和统计信息

## 目录结构

```
paper/
├── pdf_chaser.py          # 主程序
├── run_pdf_chaser.py      # 启动脚本
├── README.md              # 说明文档
└── data/
    ├── doc_info/          # 存放xls文件的目录
    │   ├── savedrecs (1).xls
    │   ├── savedrecs (2).xls
    │   └── ...
    ├── doc_pdf/           # 下载的PDF文件存放目录
    ├── download_progress.json  # 下载进度文件
    └── pdf_chaser.log     # 日志文件
```

## 使用方法

### 1. 准备数据文件

将从Web of Science导出的xls文件放入 `data/doc_info/` 目录下。

### 2. 运行程序

有两种运行方式：

#### 方式一：使用启动脚本（推荐）

```bash
python run_pdf_chaser.py
```

#### 方式二：直接运行主程序

```bash
python pdf_chaser.py
```

### 3. 程序运行流程

1. **合并xls文件**：自动读取并合并所有xls文件
2. **提取DOI**：从合并后的数据中提取有效的DOI
3. **检查进度**：加载之前的下载进度（如果存在）
4. **批量下载**：逐个从Sci-Hub下载PDF文件
5. **保存进度**：每下载10个文件自动保存进度

## 功能特性

### 防反爬虫机制

- **随机延迟**：每次请求间隔2-5秒随机延迟
- **多User-Agent**：随机使用不同的浏览器标识
- **多镜像站点**：自动尝试不同的Sci-Hub镜像
- **错误重试**：网络错误时自动重试

### 中断恢复功能

- 程序会自动保存下载进度到 `download_progress.json`
- 支持 Ctrl+C 中断，下次运行时会跳过已下载的文件
- 每下载10个文件自动保存一次进度

### 文件命名规则

- PDF文件以DOI命名，将 `/` 替换为 `_`
- 例如：`10.1186/s41239-019-0171-0` → `10.1186_s41239-019-0171-0.pdf`
- 自动处理文件名中的非法字符

## 配置选项

可以在 `pdf_chaser.py` 中修改以下配置：

```python
# 下载配置
self.min_delay = 2      # 最小延迟秒数
self.max_delay = 5      # 最大延迟秒数
self.max_retries = 3    # 最大重试次数
self.timeout = 30       # 请求超时时间

# Sci-Hub镜像站点
self.scihub_urls = [
    "https://sci-hub.se",
    "https://sci-hub.st", 
    "https://sci-hub.ru",
    "https://sci-hub.red",
    "https://sci-hub.mksa.top"
]
```

## 注意事项

1. **网络环境**：确保网络连接稳定，能够访问Sci-Hub
2. **存储空间**：确保有足够的磁盘空间存储PDF文件
3. **下载时间**：大量文件下载需要较长时间，建议在网络稳定时运行
4. **法律合规**：请确保下载行为符合当地法律法规和学术伦理
5. **尊重服务器**：程序已内置延迟机制，请勿修改为过于频繁的请求

## 依赖库

```bash
pip install pandas requests openpyxl
```

## 日志和统计

程序运行时会生成详细的日志文件 `data/pdf_chaser.log`，包含：

- 文件合并过程
- DOI提取结果
- 每个文件的下载状态
- 错误信息和重试记录
- 最终统计信息

运行完成后会显示统计信息：

- 总DOI数量
- 成功下载数量
- 失败数量
- 总成功率

## 故障排除

### 常见问题

1. **找不到xls文件**

   - 检查 `data/doc_info/` 目录是否存在
   - 确认xls文件已正确放置
2. **下载失败率高**

   - 检查网络连接
   - 尝试更换网络环境
   - 查看日志文件了解具体错误
3. **程序运行缓慢**

   - 这是正常现象，防反爬虫机制会增加延迟
   - 可以适当调整延迟参数，但不建议设置过小

### 重新开始下载

如果需要重新开始下载所有文件：

1. 删除 `data/download_progress.json` 文件
2. 清空 `data/doc_pdf/` 目录
3. 重新运行程序

## 版本信息

- 版本：1.0.0
- 作者：PDF Chaser
- 更新日期：2025-06-14
