import win32api
import win32con
import time, os

config = """
Left mouse button clicked at position: (1015, 686)
Left mouse button clicked at position: (998, 864)
Left mouse button clicked at position: (798, 653)
Left mouse button clicked at position: (938, 658)
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Number Input 1
Left mouse button clicked at position: (1024, 658)
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 8
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Key pressed: 46
Number Input 2
Left mouse button clicked at position: (1030, 778)
Left mouse button clicked at position: (1026, 858)
Left mouse button clicked at position: (814, 843)
"""

def record_mouse_and_keyboard():
    print("Recording mouse and keyboard operations. Press Ctrl+C to stop.")
    try:
        while True:
            # 获取鼠标位置
            x, y = win32api.GetCursorPos()
            # print(f"Mouse position: ({x}, {y})")

            # 检测鼠标左键是否按下
            if win32api.GetAsyncKeyState(win32con.VK_LBUTTON):
                print("Left mouse button clicked at position:", (x, y))

            # 检测键盘按键
            for key in range(0x08, 0xFF):  # 检测所有键
                if win32api.GetAsyncKeyState(key):
                    print(f"Key pressed: {key}")

            time.sleep(0.1)  # 减少 CPU 占用
    except KeyboardInterrupt:
        print("Recording stopped.")

def execute_mouse_and_keyboard():
    # 按下F9开始
    while True:
        if win32api.GetAsyncKeyState(win32con.VK_F9):
            break
        time.sleep(0.1)
    print("Executing recorded mouse and keyboard operations...")
    # 解析配置字符串
    actions = config.strip().split("\n")
    for action in actions:
        if action.startswith("Left mouse button clicked at position:"):
            pos = tuple(map(int, action.split(":")[1].strip()[1:-1].split(",")))
            win32api.SetCursorPos(pos)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, pos[0], pos[1], 0, 0)
            time.sleep(0.1)  # 模拟点击的延迟
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, pos[0], pos[1], 0, 0)

        elif action.startswith("Key pressed:"):
            key = int(action.split(":")[1].strip())
            win32api.keybd_event(key, 0, 0, 0)  # 按下键
            time.sleep(0.1)  # 模拟按键的延迟
            win32api.keybd_event(key, 0, win32con.KEYEVENTF_KEYUP, 0)  # 松开键
        time.sleep(1)


def execute_mouse_and_keyboard_with_number(marker_from, marker_to):
    # 解析配置字符串
    actions = config.strip().split("\n")
    for action in actions:
        if action.startswith("Left mouse button clicked at position:"):
            pos = tuple(map(int,
                            action.split(":")[1].strip()[1:-1].split(",")))
            win32api.SetCursorPos(pos)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, pos[0], pos[1],
                                 0, 0)
            time.sleep(0.1)  # 模拟点击的延迟
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, pos[0], pos[1],
                                 0, 0)

        elif action.startswith("Key pressed:"):
            key = int(action.split(":")[1].strip())
            win32api.keybd_event(key, 0, 0, 0)  # 按下键
            time.sleep(0.1)  # 模拟按键的延迟
            win32api.keybd_event(key, 0, win32con.KEYEVENTF_KEYUP, 0)  # 松开键

        elif action.startswith("Number Input 1"):

            for digit in str(marker_from):
                win32api.keybd_event(int(digit) + 48, 0, 0, 0)
                time.sleep(0.1)
                win32api.keybd_event(int(digit) + 48, 0, win32con.KEYEVENTF_KEYUP, 0)
                time.sleep(0.5)

        elif action.startswith("Number Input 2"):

            for digit in str(marker_to):
                win32api.keybd_event(int(digit) + 48, 0, 0, 0)
                time.sleep(0.1)
                win32api.keybd_event(int(digit) + 48, 0, win32con.KEYEVENTF_KEYUP, 0)
                time.sleep(0.5)

        time.sleep(1)

def main():
    while True:
        if win32api.GetAsyncKeyState(win32con.VK_F9):
            break
        time.sleep(0.1)
    print("Executing recorded mouse and keyboard operations...")
    download_dir = "C:\\Users\\<USER>\\Downloads"
    output_dir = "wos/2024-2026"
    for start in range(1, 70225, 1000):
        print(start)
        pos = (1037, 508)
        win32api.SetCursorPos(pos)
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, pos[0], pos[1], 0, 0)
        time.sleep(0.1)  # 模拟点击的延迟
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, pos[0], pos[1], 0, 0)
        time.sleep(2)
        # scroll to the top of the page
        win32api.keybd_event(0x21, 0, 0, 0)  # Press 'Page Up' key
        time.sleep(0.1)
        win32api.keybd_event(0x21, 0, win32con.KEYEVENTF_KEYUP, 0)
        time.sleep(2)
        execute_mouse_and_keyboard_with_number(marker_from=start,
                                                marker_to=start + 999)
        while True:
            if os.path.exists(os.path.join(download_dir, "savedrecs.xls")):
                break
            time.sleep(5)
        # Move the downloaded file to the output location
        os.rename(os.path.join(download_dir, "savedrecs.xls"), os.path.join(output_dir, f"savedrecs_{start}.xls"))

        # Add a delay if necessary to avoid overwhelming the server
        time.sleep(10)

if __name__ == "__main__":
    # record_mouse_and_keyboard()
    # execute_mouse_and_keyboard()
    main()