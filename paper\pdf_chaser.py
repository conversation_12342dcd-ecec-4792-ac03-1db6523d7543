#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Chaser - 从Sci-Hub批量下载论文PDF
功能：
1. 合并所有data/doc_info中的xls文件
2. 提取DOI列
3. 从Sci-Hub下载PDF
4. 防反爬虫机制
5. 中断恢复功能
"""

import os
import time
import random
import json
import pandas as pd
import requests
from urllib.parse import quote
from pathlib import Path
import logging
from typing import List, Set, Optional
import re

class PDFChaser:
    def __init__(self, data_dir: str = "data"):
        """
        初始化PDF下载器

        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.doc_info_dir = self.data_dir / "doc_info"
        self.doc_pdf_dir = self.data_dir / "doc_pdf"
        self.progress_file = self.data_dir / "download_progress.json"

        # 创建必要的目录
        self.doc_pdf_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志
        self.setup_logging()

        # Sci-Hub镜像站点列表
        self.scihub_urls = [
            "https://sci-hub.se",
            "https://sci-hub.st",
            "https://sci-hub.ru",
            "https://sci-hub.red",
            "https://sci-hub.mksa.top"
        ]

        # User-Agent列表用于防反爬虫
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]

        # 请求配置
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        # 下载配置
        self.min_delay = 2  # 最小延迟秒数
        self.max_delay = 5  # 最大延迟秒数
        self.max_retries = 3  # 最大重试次数
        self.timeout = 30  # 请求超时时间

    def setup_logging(self):
        """设置日志配置"""
        log_file = self.data_dir / "pdf_chaser.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def merge_xls_files(self) -> pd.DataFrame:
        """
        合并所有doc_info目录下的xls文件

        Returns:
            合并后的DataFrame
        """
        self.logger.info("开始合并xls文件...")

        if not self.doc_info_dir.exists():
            raise FileNotFoundError(f"目录不存在: {self.doc_info_dir}")

        xls_files = list(self.doc_info_dir.glob("*.xls"))
        if not xls_files:
            raise FileNotFoundError(f"在{self.doc_info_dir}中未找到xls文件")

        self.logger.info(f"找到{len(xls_files)}个xls文件")

        dataframes = []
        for file_path in xls_files:
            try:
                self.logger.info(f"读取文件: {file_path.name}")
                df = pd.read_excel(file_path)
                dataframes.append(df)
            except Exception as e:
                self.logger.error(f"读取文件{file_path.name}失败: {e}")
                continue

        if not dataframes:
            raise ValueError("没有成功读取任何xls文件")

        # 合并所有DataFrame
        merged_df = pd.concat(dataframes, ignore_index=True)
        self.logger.info(f"合并完成，总共{len(merged_df)}条记录")

        # 保存到 temp 内
        merged_df.to_csv(self.data_dir / "all_temp.csv", index=False)

        return merged_df

    def extract_dois(self, df: pd.DataFrame) -> List[str]:
        """
        从DataFrame中提取有效的DOI

        Args:
            df: 包含DOI列的DataFrame

        Returns:
            DOI列表
        """
        self.logger.info("提取DOI...")

        if 'DOI' not in df.columns:
            raise ValueError("DataFrame中没有DOI列")

        # 提取非空的DOI
        dois = df['DOI'].dropna().astype(str).tolist()

        # 过滤有效的DOI（基本格式验证）
        valid_dois = []
        doi_pattern = re.compile(r'^10\.\d+/.+')

        for doi in dois:
            doi = doi.strip()
            if doi and doi != 'nan' and doi_pattern.match(doi):
                valid_dois.append(doi)

        # 去重
        unique_dois = list(set(valid_dois))

        self.logger.info(f"提取到{len(unique_dois)}个有效的唯一DOI")
        return unique_dois

    def load_progress(self) -> Set[str]:
        """
        加载下载进度

        Returns:
            已下载的DOI集合
        """
        if not self.progress_file.exists():
            return set()

        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
                downloaded_dois = set(progress_data.get('downloaded', []))
                self.logger.info(f"加载进度：已下载{len(downloaded_dois)}个PDF")
                return downloaded_dois
        except Exception as e:
            self.logger.error(f"加载进度文件失败: {e}")
            return set()

    def save_progress(self, downloaded_dois: Set[str]):
        """
        保存下载进度

        Args:
            downloaded_dois: 已下载的DOI集合
        """
        try:
            progress_data = {
                'downloaded': list(downloaded_dois),
                'last_update': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存进度文件失败: {e}")

    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def random_delay(self):
        """随机延迟"""
        delay = random.uniform(self.min_delay, self.max_delay)
        time.sleep(delay)

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不合法字符

        Args:
            filename: 原始文件名

        Returns:
            清理后的文件名
        """
        # 移除或替换不合法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            filename = filename.replace(char, '_')

        # 限制文件名长度
        if len(filename) > 200:
            filename = filename[:200]

        return filename

    def download_pdf_from_scihub(self, doi: str) -> Optional[str]:
        """
        从Sci-Hub下载单个PDF

        Args:
            doi: DOI标识符

        Returns:
            成功时返回保存的文件路径，失败时返回None
        """
        self.logger.info(f"开始下载DOI: {doi}")

        # 生成文件名
        safe_doi = self.sanitize_filename(doi.replace('/', '_'))
        pdf_filename = f"{safe_doi}.pdf"
        pdf_path = self.doc_pdf_dir / pdf_filename

        # 如果文件已存在，跳过
        if pdf_path.exists():
            self.logger.info(f"文件已存在，跳过: {pdf_filename}")
            return str(pdf_path)

        # 尝试不同的Sci-Hub镜像
        for scihub_url in self.scihub_urls:
            try:
                # 构建请求URL
                search_url = f"{scihub_url}/{quote(doi)}"

                # 设置随机User-Agent
                headers = {
                    'User-Agent': self.get_random_user_agent(),
                    'Referer': scihub_url,
                }

                self.logger.info(f"尝试从 {scihub_url} 下载")

                # 发送请求获取页面
                response = self.session.get(
                    search_url,
                    headers=headers,
                    timeout=self.timeout,
                    allow_redirects=True
                )
                response.raise_for_status()

                # 查找PDF下载链接
                pdf_url = self.extract_pdf_url(response.text, scihub_url)
                if not pdf_url:
                    self.logger.warning(f"在 {scihub_url} 未找到PDF链接")
                    continue

                # 下载PDF文件
                self.logger.info(f"下载PDF: {pdf_url}")
                pdf_response = self.session.get(
                    pdf_url,
                    headers=headers,
                    timeout=self.timeout * 2,
                    stream=True
                )
                pdf_response.raise_for_status()

                # 检查响应是否为PDF
                content_type = pdf_response.headers.get('content-type', '').lower()
                if 'pdf' not in content_type:
                    self.logger.warning(f"响应不是PDF格式: {content_type}")
                    continue

                # 保存PDF文件
                with open(pdf_path, 'wb') as f:
                    for chunk in pdf_response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                # 验证文件大小
                if pdf_path.stat().st_size < 1024:  # 小于1KB可能是错误页面
                    pdf_path.unlink()
                    self.logger.warning(f"下载的文件太小，可能是错误页面")
                    continue

                self.logger.info(f"成功下载: {pdf_filename}")
                return str(pdf_path)

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"从 {scihub_url} 下载失败: {e}")
                continue
            except Exception as e:
                self.logger.error(f"下载过程中出现错误: {e}")
                continue

        self.logger.error(f"所有镜像都无法下载DOI: {doi}")
        return None

    def extract_pdf_url(self, html_content: str, base_url: str) -> Optional[str]:
        """
        从Sci-Hub页面HTML中提取PDF下载链接

        Args:
            html_content: HTML页面内容
            base_url: 基础URL

        Returns:
            PDF下载链接，如果未找到则返回None
        """
        import re
        from urllib.parse import urljoin, urlparse

        # 常见的PDF链接模式
        patterns = [
            r'<iframe[^>]+src=["\']([^"\']+\.pdf[^"\']*)["\']',
            r'<embed[^>]+src=["\']([^"\']+\.pdf[^"\']*)["\']',
            r'<a[^>]+href=["\']([^"\']+\.pdf[^"\']*)["\']',
            r'location\.href\s*=\s*["\']([^"\']+\.pdf[^"\']*)["\']',
            r'window\.open\(["\']([^"\']+\.pdf[^"\']*)["\']',
            r'src=["\']([^"\']*sci-hub[^"\']*\.pdf[^"\']*)["\']',
            r'href=["\']([^"\']*\.pdf[^"\']*)["\']'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                # 清理URL
                pdf_url = match.strip()

                # 跳过明显不是PDF的链接
                if any(skip in pdf_url.lower() for skip in ['javascript:', 'mailto:', '#']):
                    continue

                # 处理相对URL
                if pdf_url.startswith('//'):
                    pdf_url = 'https:' + pdf_url
                elif pdf_url.startswith('/'):
                    pdf_url = urljoin(base_url, pdf_url)
                elif not pdf_url.startswith('http'):
                    pdf_url = urljoin(base_url, pdf_url)

                # 验证URL格式
                try:
                    parsed = urlparse(pdf_url)
                    if parsed.scheme and parsed.netloc:
                        return pdf_url
                except:
                    continue

        return None

    def download_all_pdfs(self, dois: List[str]) -> dict:
        """
        批量下载所有PDF

        Args:
            dois: DOI列表

        Returns:
            下载结果统计
        """
        self.logger.info(f"开始批量下载{len(dois)}个PDF...")

        # 加载已下载的进度
        downloaded_dois = self.load_progress()

        # 过滤已下载的DOI
        remaining_dois = [doi for doi in dois if doi not in downloaded_dois]
        self.logger.info(f"需要下载{len(remaining_dois)}个PDF（已跳过{len(downloaded_dois)}个）")

        # 统计信息
        stats = {
            'total': len(dois),
            'already_downloaded': len(downloaded_dois),
            'to_download': len(remaining_dois),
            'successful': 0,
            'failed': 0,
            'skipped': len(downloaded_dois)
        }

        # 开始下载
        for i, doi in enumerate(remaining_dois, 1):
            try:
                self.logger.info(f"进度: {i}/{len(remaining_dois)} - 下载DOI: {doi}")

                # 下载PDF
                result = self.download_pdf_from_scihub(doi)

                if result:
                    downloaded_dois.add(doi)
                    stats['successful'] += 1
                    self.logger.info(f"✓ 成功下载: {doi}")
                else:
                    stats['failed'] += 1
                    self.logger.error(f"✗ 下载失败: {doi}")

                # 每下载10个文件保存一次进度
                if i % 10 == 0:
                    self.save_progress(downloaded_dois)
                    self.logger.info(f"已保存进度，成功: {stats['successful']}, 失败: {stats['failed']}")

                # 随机延迟防止被封
                if i < len(remaining_dois):  # 最后一个不需要延迟
                    self.random_delay()

            except KeyboardInterrupt:
                self.logger.info("用户中断下载，保存进度...")
                self.save_progress(downloaded_dois)
                break
            except Exception as e:
                self.logger.error(f"下载DOI {doi} 时出现未知错误: {e}")
                stats['failed'] += 1
                continue

        # 保存最终进度
        self.save_progress(downloaded_dois)

        # 输出统计信息
        self.logger.info("=" * 50)
        self.logger.info("下载完成！统计信息：")
        self.logger.info(f"总计DOI数量: {stats['total']}")
        self.logger.info(f"已跳过（之前下载）: {stats['skipped']}")
        self.logger.info(f"本次成功下载: {stats['successful']}")
        self.logger.info(f"本次下载失败: {stats['failed']}")
        self.logger.info(f"总成功率: {(stats['successful'] + stats['skipped']) / stats['total'] * 100:.1f}%")
        self.logger.info("=" * 50)

        return stats

    def run(self):
        """
        运行主程序
        """
        try:
            self.logger.info("PDF Chaser 启动...")

            # 1. 合并xls文件
            merged_df = self.merge_xls_files()

            # 2. 提取DOI
            dois = self.extract_dois(merged_df)

            if not dois:
                self.logger.error("没有找到有效的DOI，程序退出")
                return

            # 3. 开始下载
            stats = self.download_all_pdfs(dois)

            self.logger.info("程序执行完成！")

        except Exception as e:
            self.logger.error(f"程序执行失败: {e}")
            raise


def main():
    """主函数"""
    # 切换到paper目录
    import os
    if os.path.basename(os.getcwd()) != 'paper':
        if os.path.exists('paper'):
            os.chdir('paper')
        else:
            print("错误：请在包含paper目录的路径下运行此脚本")
            return

    # 创建下载器并运行
    chaser = PDFChaser()
    chaser.run()


if __name__ == "__main__":
    main()