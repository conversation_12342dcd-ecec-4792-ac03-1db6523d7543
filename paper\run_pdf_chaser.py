#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Chaser 启动脚本
用于批量下载论文PDF的便捷启动器
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("=" * 60)
    print("PDF Chaser - 论文PDF批量下载工具")
    print("=" * 60)
    print()
    
    # 检查当前目录
    current_dir = Path.cwd()
    if current_dir.name != 'paper':
        # 尝试切换到paper目录
        paper_dir = current_dir / 'paper'
        if paper_dir.exists():
            os.chdir(paper_dir)
            print(f"已切换到目录: {paper_dir}")
        else:
            print("错误：请在包含paper目录的路径下运行此脚本")
            print(f"当前目录: {current_dir}")
            return 1
    
    # 检查必要的目录和文件
    data_dir = Path('data')
    doc_info_dir = data_dir / 'doc_info'
    
    if not doc_info_dir.exists():
        print(f"错误：找不到数据目录 {doc_info_dir}")
        print("请确保data/doc_info目录存在并包含xls文件")
        return 1
    
    xls_files = list(doc_info_dir.glob('*.xls'))
    if not xls_files:
        print(f"错误：在 {doc_info_dir} 中未找到xls文件")
        print("请确保该目录包含从Web of Science导出的xls文件")
        return 1
    
    print(f"找到 {len(xls_files)} 个xls文件")
    print("文件列表:")
    for i, file_path in enumerate(xls_files, 1):
        print(f"  {i}. {file_path.name}")
    print()
    
    # 询问用户是否继续
    while True:
        choice = input("是否开始下载PDF？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            break
        elif choice in ['n', 'no', '否']:
            print("用户取消操作")
            return 0
        else:
            print("请输入 y 或 n")
    
    print()
    print("开始运行PDF Chaser...")
    print("注意：")
    print("- 下载过程可能需要较长时间")
    print("- 程序会自动保存进度，可以随时按Ctrl+C中断")
    print("- 中断后再次运行会从上次停止的地方继续")
    print("- 下载的PDF文件保存在 data/doc_pdf/ 目录下")
    print("- 日志文件保存在 data/pdf_chaser.log")
    print()
    
    try:
        # 导入并运行PDF Chaser
        from pdf_chaser import PDFChaser
        
        chaser = PDFChaser()
        chaser.run()
        
        print()
        print("=" * 60)
        print("PDF Chaser 运行完成！")
        print("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        print()
        print("用户中断程序")
        print("进度已保存，下次运行时会从中断处继续")
        return 0
    except Exception as e:
        print(f"程序运行出错: {e}")
        print("请检查日志文件 data/pdf_chaser.log 获取详细信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
